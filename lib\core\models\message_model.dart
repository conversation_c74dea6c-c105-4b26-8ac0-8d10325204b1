import 'package:json_annotation/json_annotation.dart';
import '../constants/app_constants.dart';

part 'message_model.g.dart';

/// Message model for Kallami chat application
@JsonSerializable()
class MessageModel {
  final String id;
  final String chatId;
  final String senderId;
  final String senderName;
  final String content;
  final String type; // text, image, video, audio, document
  final String? mediaUrl;
  final String? thumbnailUrl;
  final Map<String, dynamic>? metadata;
  final DateTime timestamp;
  final String status; // sent, delivered, seen
  final String? replyToMessageId;
  final List<String> seenBy;
  final List<MessageReaction> reactions;
  final bool isDeleted;
  final DateTime? deletedAt;

  const MessageModel({
    required this.id,
    required this.chatId,
    required this.senderId,
    required this.senderName,
    required this.content,
    this.type = AppConstants.textMessage,
    this.mediaUrl,
    this.thumbnailUrl,
    this.metadata,
    required this.timestamp,
    this.status = AppConstants.messageSent,
    this.replyToMessageId,
    this.seenBy = const [],
    this.reactions = const [],
    this.isDeleted = false,
    this.deletedAt,
  });

  /// Create MessageModel from JSON
  factory MessageModel.fromJson(Map<String, dynamic> json) =>
      _$MessageModelFromJson(json);

  /// Convert MessageModel to JSON
  Map<String, dynamic> toJson() => _$MessageModelToJson(this);

  /// Create a copy of MessageModel with updated fields
  MessageModel copyWith({
    String? id,
    String? chatId,
    String? senderId,
    String? senderName,
    String? content,
    String? type,
    String? mediaUrl,
    String? thumbnailUrl,
    Map<String, dynamic>? metadata,
    DateTime? timestamp,
    String? status,
    String? replyToMessageId,
    List<String>? seenBy,
    List<MessageReaction>? reactions,
    bool? isDeleted,
    DateTime? deletedAt,
  }) {
    return MessageModel(
      id: id ?? this.id,
      chatId: chatId ?? this.chatId,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      content: content ?? this.content,
      type: type ?? this.type,
      mediaUrl: mediaUrl ?? this.mediaUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      metadata: metadata ?? this.metadata,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
      replyToMessageId: replyToMessageId ?? this.replyToMessageId,
      seenBy: seenBy ?? this.seenBy,
      reactions: reactions ?? this.reactions,
      isDeleted: isDeleted ?? this.isDeleted,
      deletedAt: deletedAt ?? this.deletedAt,
    );
  }

  /// Check if message is text type
  bool get isTextMessage => type == AppConstants.textMessage;

  /// Check if message is image type
  bool get isImageMessage => type == AppConstants.imageMessage;

  /// Check if message is video type
  bool get isVideoMessage => type == AppConstants.videoMessage;

  /// Check if message is audio type
  bool get isAudioMessage => type == AppConstants.audioMessage;

  /// Check if message is document type
  bool get isDocumentMessage => type == AppConstants.documentMessage;

  /// Check if message has media
  bool get hasMedia => mediaUrl != null && mediaUrl!.isNotEmpty;

  /// Check if message is a reply
  bool get isReply => replyToMessageId != null;

  /// Check if message is seen
  bool get isSeen => status == AppConstants.messageSeen;

  /// Check if message is delivered
  bool get isDelivered => status == AppConstants.messageDelivered;

  /// Check if message is sent
  bool get isSent => status == AppConstants.messageSent;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'MessageModel(id: $id, senderId: $senderId, type: $type, timestamp: $timestamp)';
  }
}

/// Message reaction model
@JsonSerializable()
class MessageReaction {
  final String userId;
  final String userName;
  final String emoji;
  final DateTime timestamp;

  const MessageReaction({
    required this.userId,
    required this.userName,
    required this.emoji,
    required this.timestamp,
  });

  /// Create MessageReaction from JSON
  factory MessageReaction.fromJson(Map<String, dynamic> json) =>
      _$MessageReactionFromJson(json);

  /// Convert MessageReaction to JSON
  Map<String, dynamic> toJson() => _$MessageReactionToJson(this);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MessageReaction &&
        other.userId == userId &&
        other.emoji == emoji;
  }

  @override
  int get hashCode => userId.hashCode ^ emoji.hashCode;

  @override
  String toString() {
    return 'MessageReaction(userId: $userId, emoji: $emoji)';
  }
}
