// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appName => 'كلامي';

  @override
  String get appSlogan => 'كل صوت مهم. كل رسالة تحكي قصة.';

  @override
  String get welcome => 'مرحباً';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get register => 'إنشاء حساب';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get confirmPassword => 'تأكيد كلمة المرور';

  @override
  String get forgotPassword => 'نسيت كلمة المرور؟';

  @override
  String get resetPassword => 'إعادة تعيين كلمة المرور';

  @override
  String get signInWithGoogle => 'تسجيل الدخول بجوجل';

  @override
  String get createAccount => 'إنشاء حساب جديد';

  @override
  String get alreadyHaveAccount => 'لديك حساب بالفعل؟';

  @override
  String get dontHaveAccount => 'ليس لديك حساب؟';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get editProfile => 'تعديل الملف الشخصي';

  @override
  String get name => 'الاسم';

  @override
  String get about => 'حول التطبيق';

  @override
  String get phoneNumber => 'رقم الهاتف';

  @override
  String get profilePicture => 'صورة الملف الشخصي';

  @override
  String get changeProfilePicture => 'تغيير صورة الملف الشخصي';

  @override
  String get camera => 'الكاميرا';

  @override
  String get gallery => 'المعرض';

  @override
  String get chats => 'المحادثات';

  @override
  String get newChat => 'محادثة جديدة';

  @override
  String get newGroup => 'مجموعة جديدة';

  @override
  String get searchChats => 'البحث في المحادثات';

  @override
  String get noChats => 'لا توجد محادثات';

  @override
  String get startNewChat => 'ابدأ محادثة جديدة';

  @override
  String get typeMessage => 'اكتب رسالة...';

  @override
  String get sendMessage => 'إرسال';

  @override
  String get voiceMessage => 'رسالة صوتية';

  @override
  String get videoCall => 'مكالمة فيديو';

  @override
  String get voiceCall => 'مكالمة صوتية';

  @override
  String get attachFile => 'إرفاق ملف';

  @override
  String get takePhoto => 'التقاط صورة';

  @override
  String get recordVideo => 'تسجيل فيديو';

  @override
  String get selectDocument => 'اختيار مستند';

  @override
  String get online => 'متصل';

  @override
  String get offline => 'غير متصل';

  @override
  String get lastSeen => 'آخر ظهور';

  @override
  String get typing => 'يكتب...';

  @override
  String get recording => 'يسجل...';

  @override
  String get messageSent => 'تم الإرسال';

  @override
  String get messageDelivered => 'تم التسليم';

  @override
  String get messageSeen => 'تم القراءة';

  @override
  String get reply => 'رد';

  @override
  String get forward => 'إعادة توجيه';

  @override
  String get copy => 'نسخ';

  @override
  String get delete => 'حذف';

  @override
  String get deleteForMe => 'حذف لي';

  @override
  String get deleteForEveryone => 'حذف للجميع';

  @override
  String get settings => 'الإعدادات';

  @override
  String get language => 'اللغة';

  @override
  String get theme => 'المظهر';

  @override
  String get lightTheme => 'فاتح';

  @override
  String get darkTheme => 'داكن';

  @override
  String get systemTheme => 'النظام';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get privacy => 'الخصوصية';

  @override
  String get security => 'الأمان';

  @override
  String get help => 'المساعدة';

  @override
  String get logout => 'تسجيل الخروج';

  @override
  String get privacySettings => 'إعدادات الخصوصية';

  @override
  String get lastSeenPrivacy => 'آخر ظهور';

  @override
  String get profilePhotoPrivacy => 'صورة الملف الشخصي';

  @override
  String get statusPrivacy => 'الحالة';

  @override
  String get everyone => 'الجميع';

  @override
  String get contacts => 'جهات الاتصال';

  @override
  String get nobody => 'لا أحد';

  @override
  String get securitySettings => 'إعدادات الأمان';

  @override
  String get appLock => 'قفل التطبيق';

  @override
  String get biometricAuth => 'المصادقة البيومترية';

  @override
  String get changePin => 'تغيير الرقم السري';

  @override
  String get twoFactorAuth => 'المصادقة الثنائية';

  @override
  String get notificationSettings => 'إعدادات الإشعارات';

  @override
  String get messageNotifications => 'إشعارات الرسائل';

  @override
  String get callNotifications => 'إشعارات المكالمات';

  @override
  String get groupNotifications => 'إشعارات المجموعات';

  @override
  String get notificationSound => 'صوت الإشعار';

  @override
  String get vibration => 'الاهتزاز';

  @override
  String get callHistory => 'سجل المكالمات';

  @override
  String get incomingCall => 'مكالمة واردة';

  @override
  String get outgoingCall => 'مكالمة صادرة';

  @override
  String get missedCall => 'مكالمة فائتة';

  @override
  String get callDuration => 'مدة المكالمة';

  @override
  String get accept => 'قبول';

  @override
  String get decline => 'رفض';

  @override
  String get endCall => 'إنهاء المكالمة';

  @override
  String get mute => 'كتم الصوت';

  @override
  String get unmute => 'إلغاء كتم الصوت';

  @override
  String get speaker => 'مكبر الصوت';

  @override
  String get switchCamera => 'تبديل الكاميرا';

  @override
  String get error => 'خطأ';

  @override
  String get success => 'نجح';

  @override
  String get warning => 'تحذير';

  @override
  String get info => 'معلومات';

  @override
  String get ok => 'موافق';

  @override
  String get cancel => 'إلغاء';

  @override
  String get save => 'حفظ';

  @override
  String get edit => 'تعديل';

  @override
  String get done => 'تم';

  @override
  String get next => 'التالي';

  @override
  String get previous => 'السابق';

  @override
  String get skip => 'تخطي';

  @override
  String get retry => 'إعادة المحاولة';

  @override
  String get connectionError => 'خطأ في الاتصال';

  @override
  String get noInternetConnection => 'لا يوجد اتصال بالإنترنت';

  @override
  String get serverError => 'خطأ في الخادم';

  @override
  String get unknownError => 'خطأ غير معروف';

  @override
  String get permissionRequired => 'إذن مطلوب';

  @override
  String get cameraPermission => 'إذن الكاميرا مطلوب';

  @override
  String get microphonePermission => 'إذن الميكروفون مطلوب';

  @override
  String get storagePermission => 'إذن التخزين مطلوب';

  @override
  String get contactsPermission => 'إذن جهات الاتصال مطلوب';

  @override
  String get locationPermission => 'إذن الموقع مطلوب';

  @override
  String get block => 'حظر';

  @override
  String get unblock => 'إلغاء الحظر';

  @override
  String get report => 'إبلاغ';

  @override
  String get blockUser => 'حظر المستخدم';

  @override
  String get reportUser => 'إبلاغ عن المستخدم';

  @override
  String get blockedUsers => 'المستخدمون المحظورون';

  @override
  String get searchMessages => 'البحث في الرسائل';

  @override
  String get searchResults => 'نتائج البحث';

  @override
  String get noResults => 'لا توجد نتائج';

  @override
  String get selectLanguage => 'اختر اللغة';

  @override
  String get arabic => 'العربية';

  @override
  String get english => 'English';

  @override
  String get onboardingTitle1 => 'مرحباً بك في كلامي';

  @override
  String get onboardingDesc1 =>
      'تطبيق المحادثة الحديث المصمم خصيصاً للمستخدمين العرب';

  @override
  String get onboardingTitle2 => 'تواصل بسهولة';

  @override
  String get onboardingDesc2 =>
      'أرسل الرسائل النصية والصوتية والمرئية مع أصدقائك وعائلتك';

  @override
  String get onboardingTitle3 => 'مكالمات عالية الجودة';

  @override
  String get onboardingDesc3 => 'استمتع بمكالمات صوتية ومرئية واضحة وآمنة';

  @override
  String get onboardingTitle4 => 'أمان وخصوصية';

  @override
  String get onboardingDesc4 => 'رسائلك محمية بتشفير من طرف إلى طرف';

  @override
  String get getStarted => 'ابدأ الآن';
}
