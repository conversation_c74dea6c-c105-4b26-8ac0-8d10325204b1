/// App-wide constants for Kallami chat application
class AppConstants {
  // App Info
  static const String appName = 'Kallami';
  static const String appSlogan = 'Every voice matters. Every message tells a story.';
  static const String appVersion = '1.0.0';
  
  // Supported Languages
  static const String defaultLanguage = 'ar';
  static const List<String> supportedLanguages = ['ar', 'en'];
  
  // Firebase Collections
  static const String usersCollection = 'users';
  static const String chatsCollection = 'chats';
  static const String messagesCollection = 'messages';
  static const String callLogsCollection = 'call_logs';
  
  // Storage Paths
  static const String profilePicturesPath = 'profile_pictures';
  static const String chatMediaPath = 'chat_media';
  static const String voiceNotesPath = 'voice_notes';
  
  // Shared Preferences Keys
  static const String languageKey = 'language';
  static const String themeKey = 'theme';
  static const String userIdKey = 'user_id';
  static const String isFirstLaunchKey = 'is_first_launch';
  static const String biometricEnabledKey = 'biometric_enabled';
  
  // Message Types
  static const String textMessage = 'text';
  static const String imageMessage = 'image';
  static const String videoMessage = 'video';
  static const String audioMessage = 'audio';
  static const String documentMessage = 'document';
  
  // Call Types
  static const String voiceCall = 'voice';
  static const String videoCall = 'video';
  
  // Message Status
  static const String messageSent = 'sent';
  static const String messageDelivered = 'delivered';
  static const String messageSeen = 'seen';
  
  // Limits
  static const int maxMessageLength = 4000;
  static const int maxGroupMembers = 256;
  static const int maxFileSize = 100 * 1024 * 1024; // 100MB
  static const int maxVoiceNoteDuration = 300; // 5 minutes
}
