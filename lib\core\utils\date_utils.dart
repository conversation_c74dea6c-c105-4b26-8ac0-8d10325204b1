import 'package:intl/intl.dart';

/// Utility class for date and time formatting in Arabic
class AppDateUtils {
  // Private constructor to prevent instantiation
  AppDateUtils._();

  /// Format date for chat list (Arabic)
  static String formatChatListDate(DateTime date, {bool isArabic = true}) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final messageDate = DateTime(date.year, date.month, date.day);

    if (messageDate == today) {
      // Today - show time
      return isArabic 
        ? DateFormat('HH:mm', 'ar').format(date)
        : DateFormat('HH:mm').format(date);
    } else if (messageDate == yesterday) {
      // Yesterday
      return isArabic ? 'أمس' : 'Yesterday';
    } else if (now.difference(date).inDays < 7) {
      // This week - show day name
      return isArabic 
        ? DateFormat('EEEE', 'ar').format(date)
        : DateFormat('EEEE').format(date);
    } else if (date.year == now.year) {
      // This year - show month and day
      return isArabic 
        ? DateFormat('d MMMM', 'ar').format(date)
        : DateFormat('MMM d').format(date);
    } else {
      // Different year - show full date
      return isArabic 
        ? DateFormat('d/M/yyyy', 'ar').format(date)
        : DateFormat('M/d/yyyy').format(date);
    }
  }

  /// Format date for message timestamp (Arabic)
  static String formatMessageTime(DateTime date, {bool isArabic = true}) {
    return isArabic 
      ? DateFormat('HH:mm', 'ar').format(date)
      : DateFormat('HH:mm').format(date);
  }

  /// Format date for message date separator (Arabic)
  static String formatMessageDate(DateTime date, {bool isArabic = true}) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final messageDate = DateTime(date.year, date.month, date.day);

    if (messageDate == today) {
      return isArabic ? 'اليوم' : 'Today';
    } else if (messageDate == yesterday) {
      return isArabic ? 'أمس' : 'Yesterday';
    } else if (date.year == now.year) {
      return isArabic 
        ? DateFormat('EEEE، d MMMM', 'ar').format(date)
        : DateFormat('EEEE, MMMM d').format(date);
    } else {
      return isArabic 
        ? DateFormat('EEEE، d MMMM yyyy', 'ar').format(date)
        : DateFormat('EEEE, MMMM d, yyyy').format(date);
    }
  }

  /// Format last seen time (Arabic)
  static String formatLastSeen(DateTime date, {bool isArabic = true}) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inMinutes < 1) {
      return isArabic ? 'متصل الآن' : 'Online now';
    } else if (difference.inMinutes < 60) {
      final minutes = difference.inMinutes;
      return isArabic 
        ? 'آخر ظهور منذ $minutes دقيقة'
        : 'Last seen $minutes minutes ago';
    } else if (difference.inHours < 24) {
      final hours = difference.inHours;
      return isArabic 
        ? 'آخر ظهور منذ $hours ساعة'
        : 'Last seen $hours hours ago';
    } else if (difference.inDays < 7) {
      final days = difference.inDays;
      return isArabic 
        ? 'آخر ظهور منذ $days يوم'
        : 'Last seen $days days ago';
    } else {
      return isArabic 
        ? 'آخر ظهور ${formatChatListDate(date, isArabic: isArabic)}'
        : 'Last seen ${formatChatListDate(date, isArabic: isArabic)}';
    }
  }

  /// Format call duration
  static String formatCallDuration(Duration duration, {bool isArabic = true}) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    final seconds = duration.inSeconds.remainder(60);

    if (hours > 0) {
      return isArabic 
        ? '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}'
        : '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    } else {
      return isArabic 
        ? '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}'
        : '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }

  /// Check if date is today
  static bool isToday(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final checkDate = DateTime(date.year, date.month, date.day);
    return checkDate == today;
  }

  /// Check if date is yesterday
  static bool isYesterday(DateTime date) {
    final now = DateTime.now();
    final yesterday = DateTime(now.year, now.month, now.day).subtract(const Duration(days: 1));
    final checkDate = DateTime(date.year, date.month, date.day);
    return checkDate == yesterday;
  }

  /// Check if date is this week
  static bool isThisWeek(DateTime date) {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    return date.isAfter(startOfWeek) && date.isBefore(endOfWeek);
  }

  /// Get time ago string (Arabic)
  static String timeAgo(DateTime date, {bool isArabic = true}) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inSeconds < 60) {
      return isArabic ? 'الآن' : 'Now';
    } else if (difference.inMinutes < 60) {
      final minutes = difference.inMinutes;
      return isArabic ? 'منذ $minutes د' : '${minutes}m ago';
    } else if (difference.inHours < 24) {
      final hours = difference.inHours;
      return isArabic ? 'منذ $hours س' : '${hours}h ago';
    } else if (difference.inDays < 7) {
      final days = difference.inDays;
      return isArabic ? 'منذ $days ي' : '${days}d ago';
    } else {
      return formatChatListDate(date, isArabic: isArabic);
    }
  }
}
