import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en'),
  ];

  /// The name of the application
  ///
  /// In ar, this message translates to:
  /// **'كلامي'**
  String get appName;

  /// The slogan of the application
  ///
  /// In ar, this message translates to:
  /// **'كل صوت مهم. كل رسالة تحكي قصة.'**
  String get appSlogan;

  /// No description provided for @welcome.
  ///
  /// In ar, this message translates to:
  /// **'مرحباً'**
  String get welcome;

  /// No description provided for @login.
  ///
  /// In ar, this message translates to:
  /// **'تسجيل الدخول'**
  String get login;

  /// No description provided for @register.
  ///
  /// In ar, this message translates to:
  /// **'إنشاء حساب'**
  String get register;

  /// No description provided for @email.
  ///
  /// In ar, this message translates to:
  /// **'البريد الإلكتروني'**
  String get email;

  /// No description provided for @password.
  ///
  /// In ar, this message translates to:
  /// **'كلمة المرور'**
  String get password;

  /// No description provided for @confirmPassword.
  ///
  /// In ar, this message translates to:
  /// **'تأكيد كلمة المرور'**
  String get confirmPassword;

  /// No description provided for @forgotPassword.
  ///
  /// In ar, this message translates to:
  /// **'نسيت كلمة المرور؟'**
  String get forgotPassword;

  /// No description provided for @resetPassword.
  ///
  /// In ar, this message translates to:
  /// **'إعادة تعيين كلمة المرور'**
  String get resetPassword;

  /// No description provided for @signInWithGoogle.
  ///
  /// In ar, this message translates to:
  /// **'تسجيل الدخول بجوجل'**
  String get signInWithGoogle;

  /// No description provided for @createAccount.
  ///
  /// In ar, this message translates to:
  /// **'إنشاء حساب جديد'**
  String get createAccount;

  /// No description provided for @alreadyHaveAccount.
  ///
  /// In ar, this message translates to:
  /// **'لديك حساب بالفعل؟'**
  String get alreadyHaveAccount;

  /// No description provided for @dontHaveAccount.
  ///
  /// In ar, this message translates to:
  /// **'ليس لديك حساب؟'**
  String get dontHaveAccount;

  /// No description provided for @profile.
  ///
  /// In ar, this message translates to:
  /// **'الملف الشخصي'**
  String get profile;

  /// No description provided for @editProfile.
  ///
  /// In ar, this message translates to:
  /// **'تعديل الملف الشخصي'**
  String get editProfile;

  /// No description provided for @name.
  ///
  /// In ar, this message translates to:
  /// **'الاسم'**
  String get name;

  /// No description provided for @about.
  ///
  /// In ar, this message translates to:
  /// **'حول التطبيق'**
  String get about;

  /// No description provided for @phoneNumber.
  ///
  /// In ar, this message translates to:
  /// **'رقم الهاتف'**
  String get phoneNumber;

  /// No description provided for @profilePicture.
  ///
  /// In ar, this message translates to:
  /// **'صورة الملف الشخصي'**
  String get profilePicture;

  /// No description provided for @changeProfilePicture.
  ///
  /// In ar, this message translates to:
  /// **'تغيير صورة الملف الشخصي'**
  String get changeProfilePicture;

  /// No description provided for @camera.
  ///
  /// In ar, this message translates to:
  /// **'الكاميرا'**
  String get camera;

  /// No description provided for @gallery.
  ///
  /// In ar, this message translates to:
  /// **'المعرض'**
  String get gallery;

  /// No description provided for @chats.
  ///
  /// In ar, this message translates to:
  /// **'المحادثات'**
  String get chats;

  /// No description provided for @newChat.
  ///
  /// In ar, this message translates to:
  /// **'محادثة جديدة'**
  String get newChat;

  /// No description provided for @newGroup.
  ///
  /// In ar, this message translates to:
  /// **'مجموعة جديدة'**
  String get newGroup;

  /// No description provided for @searchChats.
  ///
  /// In ar, this message translates to:
  /// **'البحث في المحادثات'**
  String get searchChats;

  /// No description provided for @noChats.
  ///
  /// In ar, this message translates to:
  /// **'لا توجد محادثات'**
  String get noChats;

  /// No description provided for @startNewChat.
  ///
  /// In ar, this message translates to:
  /// **'ابدأ محادثة جديدة'**
  String get startNewChat;

  /// No description provided for @typeMessage.
  ///
  /// In ar, this message translates to:
  /// **'اكتب رسالة...'**
  String get typeMessage;

  /// No description provided for @sendMessage.
  ///
  /// In ar, this message translates to:
  /// **'إرسال'**
  String get sendMessage;

  /// No description provided for @voiceMessage.
  ///
  /// In ar, this message translates to:
  /// **'رسالة صوتية'**
  String get voiceMessage;

  /// No description provided for @videoCall.
  ///
  /// In ar, this message translates to:
  /// **'مكالمة فيديو'**
  String get videoCall;

  /// No description provided for @voiceCall.
  ///
  /// In ar, this message translates to:
  /// **'مكالمة صوتية'**
  String get voiceCall;

  /// No description provided for @attachFile.
  ///
  /// In ar, this message translates to:
  /// **'إرفاق ملف'**
  String get attachFile;

  /// No description provided for @takePhoto.
  ///
  /// In ar, this message translates to:
  /// **'التقاط صورة'**
  String get takePhoto;

  /// No description provided for @recordVideo.
  ///
  /// In ar, this message translates to:
  /// **'تسجيل فيديو'**
  String get recordVideo;

  /// No description provided for @selectDocument.
  ///
  /// In ar, this message translates to:
  /// **'اختيار مستند'**
  String get selectDocument;

  /// No description provided for @online.
  ///
  /// In ar, this message translates to:
  /// **'متصل'**
  String get online;

  /// No description provided for @offline.
  ///
  /// In ar, this message translates to:
  /// **'غير متصل'**
  String get offline;

  /// No description provided for @lastSeen.
  ///
  /// In ar, this message translates to:
  /// **'آخر ظهور'**
  String get lastSeen;

  /// No description provided for @typing.
  ///
  /// In ar, this message translates to:
  /// **'يكتب...'**
  String get typing;

  /// No description provided for @recording.
  ///
  /// In ar, this message translates to:
  /// **'يسجل...'**
  String get recording;

  /// No description provided for @messageSent.
  ///
  /// In ar, this message translates to:
  /// **'تم الإرسال'**
  String get messageSent;

  /// No description provided for @messageDelivered.
  ///
  /// In ar, this message translates to:
  /// **'تم التسليم'**
  String get messageDelivered;

  /// No description provided for @messageSeen.
  ///
  /// In ar, this message translates to:
  /// **'تم القراءة'**
  String get messageSeen;

  /// No description provided for @reply.
  ///
  /// In ar, this message translates to:
  /// **'رد'**
  String get reply;

  /// No description provided for @forward.
  ///
  /// In ar, this message translates to:
  /// **'إعادة توجيه'**
  String get forward;

  /// No description provided for @copy.
  ///
  /// In ar, this message translates to:
  /// **'نسخ'**
  String get copy;

  /// No description provided for @delete.
  ///
  /// In ar, this message translates to:
  /// **'حذف'**
  String get delete;

  /// No description provided for @deleteForMe.
  ///
  /// In ar, this message translates to:
  /// **'حذف لي'**
  String get deleteForMe;

  /// No description provided for @deleteForEveryone.
  ///
  /// In ar, this message translates to:
  /// **'حذف للجميع'**
  String get deleteForEveryone;

  /// No description provided for @settings.
  ///
  /// In ar, this message translates to:
  /// **'الإعدادات'**
  String get settings;

  /// No description provided for @language.
  ///
  /// In ar, this message translates to:
  /// **'اللغة'**
  String get language;

  /// No description provided for @theme.
  ///
  /// In ar, this message translates to:
  /// **'المظهر'**
  String get theme;

  /// No description provided for @lightTheme.
  ///
  /// In ar, this message translates to:
  /// **'فاتح'**
  String get lightTheme;

  /// No description provided for @darkTheme.
  ///
  /// In ar, this message translates to:
  /// **'داكن'**
  String get darkTheme;

  /// No description provided for @systemTheme.
  ///
  /// In ar, this message translates to:
  /// **'النظام'**
  String get systemTheme;

  /// No description provided for @notifications.
  ///
  /// In ar, this message translates to:
  /// **'الإشعارات'**
  String get notifications;

  /// No description provided for @privacy.
  ///
  /// In ar, this message translates to:
  /// **'الخصوصية'**
  String get privacy;

  /// No description provided for @security.
  ///
  /// In ar, this message translates to:
  /// **'الأمان'**
  String get security;

  /// No description provided for @help.
  ///
  /// In ar, this message translates to:
  /// **'المساعدة'**
  String get help;

  /// No description provided for @logout.
  ///
  /// In ar, this message translates to:
  /// **'تسجيل الخروج'**
  String get logout;

  /// No description provided for @privacySettings.
  ///
  /// In ar, this message translates to:
  /// **'إعدادات الخصوصية'**
  String get privacySettings;

  /// No description provided for @lastSeenPrivacy.
  ///
  /// In ar, this message translates to:
  /// **'آخر ظهور'**
  String get lastSeenPrivacy;

  /// No description provided for @profilePhotoPrivacy.
  ///
  /// In ar, this message translates to:
  /// **'صورة الملف الشخصي'**
  String get profilePhotoPrivacy;

  /// No description provided for @statusPrivacy.
  ///
  /// In ar, this message translates to:
  /// **'الحالة'**
  String get statusPrivacy;

  /// No description provided for @everyone.
  ///
  /// In ar, this message translates to:
  /// **'الجميع'**
  String get everyone;

  /// No description provided for @contacts.
  ///
  /// In ar, this message translates to:
  /// **'جهات الاتصال'**
  String get contacts;

  /// No description provided for @nobody.
  ///
  /// In ar, this message translates to:
  /// **'لا أحد'**
  String get nobody;

  /// No description provided for @securitySettings.
  ///
  /// In ar, this message translates to:
  /// **'إعدادات الأمان'**
  String get securitySettings;

  /// No description provided for @appLock.
  ///
  /// In ar, this message translates to:
  /// **'قفل التطبيق'**
  String get appLock;

  /// No description provided for @biometricAuth.
  ///
  /// In ar, this message translates to:
  /// **'المصادقة البيومترية'**
  String get biometricAuth;

  /// No description provided for @changePin.
  ///
  /// In ar, this message translates to:
  /// **'تغيير الرقم السري'**
  String get changePin;

  /// No description provided for @twoFactorAuth.
  ///
  /// In ar, this message translates to:
  /// **'المصادقة الثنائية'**
  String get twoFactorAuth;

  /// No description provided for @notificationSettings.
  ///
  /// In ar, this message translates to:
  /// **'إعدادات الإشعارات'**
  String get notificationSettings;

  /// No description provided for @messageNotifications.
  ///
  /// In ar, this message translates to:
  /// **'إشعارات الرسائل'**
  String get messageNotifications;

  /// No description provided for @callNotifications.
  ///
  /// In ar, this message translates to:
  /// **'إشعارات المكالمات'**
  String get callNotifications;

  /// No description provided for @groupNotifications.
  ///
  /// In ar, this message translates to:
  /// **'إشعارات المجموعات'**
  String get groupNotifications;

  /// No description provided for @notificationSound.
  ///
  /// In ar, this message translates to:
  /// **'صوت الإشعار'**
  String get notificationSound;

  /// No description provided for @vibration.
  ///
  /// In ar, this message translates to:
  /// **'الاهتزاز'**
  String get vibration;

  /// No description provided for @callHistory.
  ///
  /// In ar, this message translates to:
  /// **'سجل المكالمات'**
  String get callHistory;

  /// No description provided for @incomingCall.
  ///
  /// In ar, this message translates to:
  /// **'مكالمة واردة'**
  String get incomingCall;

  /// No description provided for @outgoingCall.
  ///
  /// In ar, this message translates to:
  /// **'مكالمة صادرة'**
  String get outgoingCall;

  /// No description provided for @missedCall.
  ///
  /// In ar, this message translates to:
  /// **'مكالمة فائتة'**
  String get missedCall;

  /// No description provided for @callDuration.
  ///
  /// In ar, this message translates to:
  /// **'مدة المكالمة'**
  String get callDuration;

  /// No description provided for @accept.
  ///
  /// In ar, this message translates to:
  /// **'قبول'**
  String get accept;

  /// No description provided for @decline.
  ///
  /// In ar, this message translates to:
  /// **'رفض'**
  String get decline;

  /// No description provided for @endCall.
  ///
  /// In ar, this message translates to:
  /// **'إنهاء المكالمة'**
  String get endCall;

  /// No description provided for @mute.
  ///
  /// In ar, this message translates to:
  /// **'كتم الصوت'**
  String get mute;

  /// No description provided for @unmute.
  ///
  /// In ar, this message translates to:
  /// **'إلغاء كتم الصوت'**
  String get unmute;

  /// No description provided for @speaker.
  ///
  /// In ar, this message translates to:
  /// **'مكبر الصوت'**
  String get speaker;

  /// No description provided for @switchCamera.
  ///
  /// In ar, this message translates to:
  /// **'تبديل الكاميرا'**
  String get switchCamera;

  /// No description provided for @error.
  ///
  /// In ar, this message translates to:
  /// **'خطأ'**
  String get error;

  /// No description provided for @success.
  ///
  /// In ar, this message translates to:
  /// **'نجح'**
  String get success;

  /// No description provided for @warning.
  ///
  /// In ar, this message translates to:
  /// **'تحذير'**
  String get warning;

  /// No description provided for @info.
  ///
  /// In ar, this message translates to:
  /// **'معلومات'**
  String get info;

  /// No description provided for @ok.
  ///
  /// In ar, this message translates to:
  /// **'موافق'**
  String get ok;

  /// No description provided for @cancel.
  ///
  /// In ar, this message translates to:
  /// **'إلغاء'**
  String get cancel;

  /// No description provided for @save.
  ///
  /// In ar, this message translates to:
  /// **'حفظ'**
  String get save;

  /// No description provided for @edit.
  ///
  /// In ar, this message translates to:
  /// **'تعديل'**
  String get edit;

  /// No description provided for @done.
  ///
  /// In ar, this message translates to:
  /// **'تم'**
  String get done;

  /// No description provided for @next.
  ///
  /// In ar, this message translates to:
  /// **'التالي'**
  String get next;

  /// No description provided for @previous.
  ///
  /// In ar, this message translates to:
  /// **'السابق'**
  String get previous;

  /// No description provided for @skip.
  ///
  /// In ar, this message translates to:
  /// **'تخطي'**
  String get skip;

  /// No description provided for @retry.
  ///
  /// In ar, this message translates to:
  /// **'إعادة المحاولة'**
  String get retry;

  /// No description provided for @connectionError.
  ///
  /// In ar, this message translates to:
  /// **'خطأ في الاتصال'**
  String get connectionError;

  /// No description provided for @noInternetConnection.
  ///
  /// In ar, this message translates to:
  /// **'لا يوجد اتصال بالإنترنت'**
  String get noInternetConnection;

  /// No description provided for @serverError.
  ///
  /// In ar, this message translates to:
  /// **'خطأ في الخادم'**
  String get serverError;

  /// No description provided for @unknownError.
  ///
  /// In ar, this message translates to:
  /// **'خطأ غير معروف'**
  String get unknownError;

  /// No description provided for @permissionRequired.
  ///
  /// In ar, this message translates to:
  /// **'إذن مطلوب'**
  String get permissionRequired;

  /// No description provided for @cameraPermission.
  ///
  /// In ar, this message translates to:
  /// **'إذن الكاميرا مطلوب'**
  String get cameraPermission;

  /// No description provided for @microphonePermission.
  ///
  /// In ar, this message translates to:
  /// **'إذن الميكروفون مطلوب'**
  String get microphonePermission;

  /// No description provided for @storagePermission.
  ///
  /// In ar, this message translates to:
  /// **'إذن التخزين مطلوب'**
  String get storagePermission;

  /// No description provided for @contactsPermission.
  ///
  /// In ar, this message translates to:
  /// **'إذن جهات الاتصال مطلوب'**
  String get contactsPermission;

  /// No description provided for @locationPermission.
  ///
  /// In ar, this message translates to:
  /// **'إذن الموقع مطلوب'**
  String get locationPermission;

  /// No description provided for @block.
  ///
  /// In ar, this message translates to:
  /// **'حظر'**
  String get block;

  /// No description provided for @unblock.
  ///
  /// In ar, this message translates to:
  /// **'إلغاء الحظر'**
  String get unblock;

  /// No description provided for @report.
  ///
  /// In ar, this message translates to:
  /// **'إبلاغ'**
  String get report;

  /// No description provided for @blockUser.
  ///
  /// In ar, this message translates to:
  /// **'حظر المستخدم'**
  String get blockUser;

  /// No description provided for @reportUser.
  ///
  /// In ar, this message translates to:
  /// **'إبلاغ عن المستخدم'**
  String get reportUser;

  /// No description provided for @blockedUsers.
  ///
  /// In ar, this message translates to:
  /// **'المستخدمون المحظورون'**
  String get blockedUsers;

  /// No description provided for @searchMessages.
  ///
  /// In ar, this message translates to:
  /// **'البحث في الرسائل'**
  String get searchMessages;

  /// No description provided for @searchResults.
  ///
  /// In ar, this message translates to:
  /// **'نتائج البحث'**
  String get searchResults;

  /// No description provided for @noResults.
  ///
  /// In ar, this message translates to:
  /// **'لا توجد نتائج'**
  String get noResults;

  /// No description provided for @selectLanguage.
  ///
  /// In ar, this message translates to:
  /// **'اختر اللغة'**
  String get selectLanguage;

  /// No description provided for @arabic.
  ///
  /// In ar, this message translates to:
  /// **'العربية'**
  String get arabic;

  /// No description provided for @english.
  ///
  /// In ar, this message translates to:
  /// **'English'**
  String get english;

  /// No description provided for @onboardingTitle1.
  ///
  /// In ar, this message translates to:
  /// **'مرحباً بك في كلامي'**
  String get onboardingTitle1;

  /// No description provided for @onboardingDesc1.
  ///
  /// In ar, this message translates to:
  /// **'تطبيق المحادثة الحديث المصمم خصيصاً للمستخدمين العرب'**
  String get onboardingDesc1;

  /// No description provided for @onboardingTitle2.
  ///
  /// In ar, this message translates to:
  /// **'تواصل بسهولة'**
  String get onboardingTitle2;

  /// No description provided for @onboardingDesc2.
  ///
  /// In ar, this message translates to:
  /// **'أرسل الرسائل النصية والصوتية والمرئية مع أصدقائك وعائلتك'**
  String get onboardingDesc2;

  /// No description provided for @onboardingTitle3.
  ///
  /// In ar, this message translates to:
  /// **'مكالمات عالية الجودة'**
  String get onboardingTitle3;

  /// No description provided for @onboardingDesc3.
  ///
  /// In ar, this message translates to:
  /// **'استمتع بمكالمات صوتية ومرئية واضحة وآمنة'**
  String get onboardingDesc3;

  /// No description provided for @onboardingTitle4.
  ///
  /// In ar, this message translates to:
  /// **'أمان وخصوصية'**
  String get onboardingTitle4;

  /// No description provided for @onboardingDesc4.
  ///
  /// In ar, this message translates to:
  /// **'رسائلك محمية بتشفير من طرف إلى طرف'**
  String get onboardingDesc4;

  /// No description provided for @getStarted.
  ///
  /// In ar, this message translates to:
  /// **'ابدأ الآن'**
  String get getStarted;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'en':
      return AppLocalizationsEn();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
