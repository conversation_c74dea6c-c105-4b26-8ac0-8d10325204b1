// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'Kallami';

  @override
  String get appSlogan => 'Every voice matters. Every message tells a story.';

  @override
  String get welcome => 'Welcome';

  @override
  String get login => 'Login';

  @override
  String get register => 'Register';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get confirmPassword => 'Confirm Password';

  @override
  String get forgotPassword => 'Forgot Password?';

  @override
  String get resetPassword => 'Reset Password';

  @override
  String get signInWithGoogle => 'Sign in with Google';

  @override
  String get createAccount => 'Create Account';

  @override
  String get alreadyHaveAccount => 'Already have an account?';

  @override
  String get dontHaveAccount => 'Don\'t have an account?';

  @override
  String get profile => 'Profile';

  @override
  String get editProfile => 'Edit Profile';

  @override
  String get name => 'Name';

  @override
  String get about => 'About';

  @override
  String get phoneNumber => 'Phone Number';

  @override
  String get profilePicture => 'Profile Picture';

  @override
  String get changeProfilePicture => 'Change Profile Picture';

  @override
  String get camera => 'Camera';

  @override
  String get gallery => 'Gallery';

  @override
  String get chats => 'Chats';

  @override
  String get newChat => 'New Chat';

  @override
  String get newGroup => 'New Group';

  @override
  String get searchChats => 'Search Chats';

  @override
  String get noChats => 'No Chats';

  @override
  String get startNewChat => 'Start New Chat';

  @override
  String get typeMessage => 'Type a message...';

  @override
  String get sendMessage => 'Send';

  @override
  String get voiceMessage => 'Voice Message';

  @override
  String get videoCall => 'Video Call';

  @override
  String get voiceCall => 'Voice Call';

  @override
  String get attachFile => 'Attach File';

  @override
  String get takePhoto => 'Take Photo';

  @override
  String get recordVideo => 'Record Video';

  @override
  String get selectDocument => 'Select Document';

  @override
  String get online => 'Online';

  @override
  String get offline => 'Offline';

  @override
  String get lastSeen => 'Last seen';

  @override
  String get typing => 'Typing...';

  @override
  String get recording => 'Recording...';

  @override
  String get messageSent => 'Sent';

  @override
  String get messageDelivered => 'Delivered';

  @override
  String get messageSeen => 'Seen';

  @override
  String get reply => 'Reply';

  @override
  String get forward => 'Forward';

  @override
  String get copy => 'Copy';

  @override
  String get delete => 'Delete';

  @override
  String get deleteForMe => 'Delete for me';

  @override
  String get deleteForEveryone => 'Delete for everyone';

  @override
  String get settings => 'Settings';

  @override
  String get language => 'Language';

  @override
  String get theme => 'Theme';

  @override
  String get lightTheme => 'Light';

  @override
  String get darkTheme => 'Dark';

  @override
  String get systemTheme => 'System';

  @override
  String get notifications => 'Notifications';

  @override
  String get privacy => 'Privacy';

  @override
  String get security => 'Security';

  @override
  String get help => 'Help';

  @override
  String get logout => 'Logout';

  @override
  String get privacySettings => 'Privacy Settings';

  @override
  String get lastSeenPrivacy => 'Last Seen';

  @override
  String get profilePhotoPrivacy => 'Profile Photo';

  @override
  String get statusPrivacy => 'Status';

  @override
  String get everyone => 'Everyone';

  @override
  String get contacts => 'Contacts';

  @override
  String get nobody => 'Nobody';

  @override
  String get securitySettings => 'Security Settings';

  @override
  String get appLock => 'App Lock';

  @override
  String get biometricAuth => 'Biometric Authentication';

  @override
  String get changePin => 'Change PIN';

  @override
  String get twoFactorAuth => 'Two-Factor Authentication';

  @override
  String get notificationSettings => 'Notification Settings';

  @override
  String get messageNotifications => 'Message Notifications';

  @override
  String get callNotifications => 'Call Notifications';

  @override
  String get groupNotifications => 'Group Notifications';

  @override
  String get notificationSound => 'Notification Sound';

  @override
  String get vibration => 'Vibration';

  @override
  String get callHistory => 'Call History';

  @override
  String get incomingCall => 'Incoming Call';

  @override
  String get outgoingCall => 'Outgoing Call';

  @override
  String get missedCall => 'Missed Call';

  @override
  String get callDuration => 'Call Duration';

  @override
  String get accept => 'Accept';

  @override
  String get decline => 'Decline';

  @override
  String get endCall => 'End Call';

  @override
  String get mute => 'Mute';

  @override
  String get unmute => 'Unmute';

  @override
  String get speaker => 'Speaker';

  @override
  String get switchCamera => 'Switch Camera';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get warning => 'Warning';

  @override
  String get info => 'Info';

  @override
  String get ok => 'OK';

  @override
  String get cancel => 'Cancel';

  @override
  String get save => 'Save';

  @override
  String get edit => 'Edit';

  @override
  String get done => 'Done';

  @override
  String get next => 'Next';

  @override
  String get previous => 'Previous';

  @override
  String get skip => 'Skip';

  @override
  String get retry => 'Retry';

  @override
  String get connectionError => 'Connection Error';

  @override
  String get noInternetConnection => 'No Internet Connection';

  @override
  String get serverError => 'Server Error';

  @override
  String get unknownError => 'Unknown Error';

  @override
  String get permissionRequired => 'Permission Required';

  @override
  String get cameraPermission => 'Camera permission is required';

  @override
  String get microphonePermission => 'Microphone permission is required';

  @override
  String get storagePermission => 'Storage permission is required';

  @override
  String get contactsPermission => 'Contacts permission is required';

  @override
  String get locationPermission => 'Location permission is required';

  @override
  String get block => 'Block';

  @override
  String get unblock => 'Unblock';

  @override
  String get report => 'Report';

  @override
  String get blockUser => 'Block User';

  @override
  String get reportUser => 'Report User';

  @override
  String get blockedUsers => 'Blocked Users';

  @override
  String get searchMessages => 'Search Messages';

  @override
  String get searchResults => 'Search Results';

  @override
  String get noResults => 'No Results';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get arabic => 'العربية';

  @override
  String get english => 'English';

  @override
  String get onboardingTitle1 => 'Welcome to Kallami';

  @override
  String get onboardingDesc1 =>
      'Modern chat application designed specifically for Arabic users';

  @override
  String get onboardingTitle2 => 'Connect Easily';

  @override
  String get onboardingDesc2 =>
      'Send text, voice, and video messages to your friends and family';

  @override
  String get onboardingTitle3 => 'High-Quality Calls';

  @override
  String get onboardingDesc3 => 'Enjoy clear and secure voice and video calls';

  @override
  String get onboardingTitle4 => 'Security & Privacy';

  @override
  String get onboardingDesc4 =>
      'Your messages are protected with end-to-end encryption';

  @override
  String get getStarted => 'Get Started';
}
