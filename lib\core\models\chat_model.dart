import 'package:json_annotation/json_annotation.dart';
import 'message_model.dart';

part 'chat_model.g.dart';

/// Chat model for Kallami chat application
@JsonSerializable()
class ChatModel {
  final String id;
  final String name;
  final String? description;
  final String? imageUrl;
  final List<String> participants;
  final Map<String, String> participantNames;
  final bool isGroup;
  final String? adminId;
  final MessageModel? lastMessage;
  final DateTime? lastMessageTime;
  final Map<String, int> unreadCounts;
  final Map<String, bool> mutedBy;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? settings;

  const ChatModel({
    required this.id,
    required this.name,
    this.description,
    this.imageUrl,
    required this.participants,
    this.participantNames = const {},
    this.isGroup = false,
    this.adminId,
    this.lastMessage,
    this.lastMessageTime,
    this.unreadCounts = const {},
    this.mutedBy = const {},
    required this.createdAt,
    required this.updatedAt,
    this.settings,
  });

  /// Create ChatModel from JSON
  factory ChatModel.fromJson(Map<String, dynamic> json) =>
      _$ChatModelFromJson(json);

  /// Convert ChatModel to JSON
  Map<String, dynamic> toJson() => _$ChatModelToJson(this);

  /// Create a copy of ChatModel with updated fields
  ChatModel copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    List<String>? participants,
    Map<String, String>? participantNames,
    bool? isGroup,
    String? adminId,
    MessageModel? lastMessage,
    DateTime? lastMessageTime,
    Map<String, int>? unreadCounts,
    Map<String, bool>? mutedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? settings,
  }) {
    return ChatModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      participants: participants ?? this.participants,
      participantNames: participantNames ?? this.participantNames,
      isGroup: isGroup ?? this.isGroup,
      adminId: adminId ?? this.adminId,
      lastMessage: lastMessage ?? this.lastMessage,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      unreadCounts: unreadCounts ?? this.unreadCounts,
      mutedBy: mutedBy ?? this.mutedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      settings: settings ?? this.settings,
    );
  }

  /// Get unread count for a specific user
  int getUnreadCount(String userId) {
    return unreadCounts[userId] ?? 0;
  }

  /// Check if chat is muted by a specific user
  bool isMutedBy(String userId) {
    return mutedBy[userId] ?? false;
  }

  /// Get other participant in a one-on-one chat
  String? getOtherParticipant(String currentUserId) {
    if (isGroup) return null;
    return participants.firstWhere(
      (id) => id != currentUserId,
      orElse: () => '',
    );
  }

  /// Get display name for the chat
  String getDisplayName(String currentUserId) {
    if (isGroup) return name;
    
    final otherParticipant = getOtherParticipant(currentUserId);
    if (otherParticipant != null && participantNames.containsKey(otherParticipant)) {
      return participantNames[otherParticipant]!;
    }
    
    return name;
  }

  /// Check if user is admin (for group chats)
  bool isAdmin(String userId) {
    return isGroup && adminId == userId;
  }

  /// Check if user is participant
  bool isParticipant(String userId) {
    return participants.contains(userId);
  }

  /// Get participant count
  int get participantCount => participants.length;

  /// Check if chat has last message
  bool get hasLastMessage => lastMessage != null;

  /// Get formatted last message preview
  String getLastMessagePreview() {
    if (lastMessage == null) return '';
    
    final message = lastMessage!;
    if (message.isDeleted) return 'تم حذف هذه الرسالة';
    
    switch (message.type) {
      case 'text':
        return message.content;
      case 'image':
        return '📷 صورة';
      case 'video':
        return '🎥 فيديو';
      case 'audio':
        return '🎵 رسالة صوتية';
      case 'document':
        return '📄 مستند';
      default:
        return message.content;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ChatModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'ChatModel(id: $id, name: $name, isGroup: $isGroup, participants: ${participants.length})';
  }
}
