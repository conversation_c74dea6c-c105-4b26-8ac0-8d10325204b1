name: kallami
description: "Kallami - Modern Arabic Chat Application. Every voice matters. Every message tells a story."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI and Design
  cupertino_icons: ^1.0.8
  material_color_utilities: ^0.11.1

  # State Management
  flutter_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5

  # Firebase
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.1
  cloud_firestore: ^5.4.3
  firebase_storage: ^12.3.2
  firebase_messaging: ^15.1.3
  firebase_analytics: ^11.3.3

  # Authentication
  google_sign_in: ^6.2.1
  local_auth: ^2.3.0

  # Internationalization
  flutter_localizations:
    sdk: flutter
  intl: ^0.20.2

  # Navigation
  go_router: ^14.6.1

  # Media and Files
  image_picker: ^1.1.2
  file_picker: ^8.1.2
  cached_network_image: ^3.4.1
  photo_view: ^0.15.0

  # Audio/Video
  camera: ^0.11.0+2
  record: ^5.1.2
  audioplayers: ^6.1.0
  permission_handler: ^11.3.1

  # WebRTC for Video Calls
  flutter_webrtc: ^0.11.7
  agora_rtc_engine: ^6.3.2

  # Notifications
  flutter_local_notifications: ^17.2.3

  # Storage and Caching
  shared_preferences: ^2.3.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Network
  dio: ^5.7.0
  connectivity_plus: ^6.0.5

  # Utils
  uuid: ^4.5.1
  timeago: ^3.7.0
  url_launcher: ^6.3.1
  package_info_plus: ^8.0.2
  device_info_plus: ^10.1.2

  # Encryption
  encrypt: ^5.0.3
  crypto: ^3.0.5

  # UI Components
  flutter_svg: ^2.0.10+1
  lottie: ^3.1.2
  shimmer: ^3.0.0
  pull_to_refresh: ^2.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  riverpod_generator: ^2.4.3
  build_runner: ^2.4.13
  json_annotation: ^4.9.0
  json_serializable: ^6.8.0

  # Testing
  mockito: ^5.4.4
  integration_test:
    sdk: flutter

  # Hive Code Generation
  hive_generator: ^2.0.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true
  generate: true # Enable internationalization

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/sounds/

  # Fonts
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
        - asset: assets/fonts/Cairo-Light.ttf
          weight: 300
        - asset: assets/fonts/Cairo-Medium.ttf
          weight: 500
        - asset: assets/fonts/Cairo-SemiBold.ttf
          weight: 600
    - family: Tajawal
      fonts:
        - asset: assets/fonts/Tajawal-Regular.ttf
        - asset: assets/fonts/Tajawal-Bold.ttf
          weight: 700
        - asset: assets/fonts/Tajawal-Light.ttf
          weight: 300
        - asset: assets/fonts/Tajawal-Medium.ttf
          weight: 500

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
