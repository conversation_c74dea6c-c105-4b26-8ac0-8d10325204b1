import 'package:flutter/material.dart';

/// App color scheme for Kallami chat application
/// Supports both light and dark themes with Arabic-friendly colors
class AppColors {
  // Primary Colors - Modern blue-green gradient
  static const Color primaryLight = Color(0xFF2196F3); // Blue
  static const Color primaryDark = Color(0xFF1976D2);
  static const Color primaryVariantLight = Color(0xFF00BCD4); // Cyan
  static const Color primaryVariantDark = Color(0xFF0097A7);
  
  // Secondary Colors - Warm accent
  static const Color secondaryLight = Color(0xFFFF9800); // Orange
  static const Color secondaryDark = Color(0xFFE65100);
  static const Color secondaryVariantLight = Color(0xFFFFC107); // Amber
  static const Color secondaryVariantDark = Color(0xFFFF8F00);
  
  // Background Colors
  static const Color backgroundLight = Color(0xFFFAFAFA);
  static const Color backgroundDark = Color(0xFF121212);
  static const Color surfaceLight = Color(0xFFFFFFFF);
  static const Color surfaceDark = Color(0xFF1E1E1E);
  
  // Chat Colors
  static const Color chatBubbleOutgoingLight = Color(0xFF2196F3);
  static const Color chatBubbleOutgoingDark = Color(0xFF1976D2);
  static const Color chatBubbleIncomingLight = Color(0xFFF5F5F5);
  static const Color chatBubbleIncomingDark = Color(0xFF2C2C2C);
  
  // Text Colors
  static const Color textPrimaryLight = Color(0xFF212121);
  static const Color textPrimaryDark = Color(0xFFFFFFFF);
  static const Color textSecondaryLight = Color(0xFF757575);
  static const Color textSecondaryDark = Color(0xFFBDBDBD);
  static const Color textOnPrimaryLight = Color(0xFFFFFFFF);
  static const Color textOnPrimaryDark = Color(0xFF000000);
  
  // Status Colors
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFF9800);
  static const Color error = Color(0xFFF44336);
  static const Color info = Color(0xFF2196F3);
  
  // Online Status
  static const Color online = Color(0xFF4CAF50);
  static const Color offline = Color(0xFF9E9E9E);
  static const Color away = Color(0xFFFF9800);
  
  // Message Status
  static const Color messageSent = Color(0xFF9E9E9E);
  static const Color messageDelivered = Color(0xFF2196F3);
  static const Color messageSeen = Color(0xFF4CAF50);
  
  // Divider Colors
  static const Color dividerLight = Color(0xFFE0E0E0);
  static const Color dividerDark = Color(0xFF424242);
  
  // Shadow Colors
  static const Color shadowLight = Color(0x1A000000);
  static const Color shadowDark = Color(0x1AFFFFFF);
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primaryLight, primaryVariantLight],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient darkGradient = LinearGradient(
    colors: [primaryDark, primaryVariantDark],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Chat Bubble Gradients
  static const LinearGradient outgoingMessageGradient = LinearGradient(
    colors: [Color(0xFF2196F3), Color(0xFF21CBF3)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient incomingMessageGradient = LinearGradient(
    colors: [Color(0xFFF5F5F5), Color(0xFFEEEEEE)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
}
